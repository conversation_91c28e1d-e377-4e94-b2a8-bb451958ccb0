{
  "target": "scanme.nmap.org",
  "open_ports": [
    {
      "port": 22,
      "protocol": "tcp",
      "service": "unknown",
      "version": "ssh-2.0",
      "banner": "SSH-2.0-OpenSSH_6.6.1p1 Ubuntu-2ubuntu2.13"
    },
    {
      "port": 80,
      "protocol": "tcp",
      "service": "unknown",
      "version": "apache/2.4.7 (ubuntu)",
      "banner": "HTTP/1.1 200 OK\r\nDate: Tue, 08 Jul 2025 20:42:43 GMT\r\nServer: Apache/2.4.7 (Ubuntu)\r\nAccept-Ranges: bytes\r\nVary: Accept-Encoding\r\nTransfer-Encoding: chunked\r\nContent-Type: text/html\r\n\r\n4c\r\n<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<title>Go ahead and ScanMe!</title>\n\r\na7f\r\n<meta name=\"viewport\" content=\"width=device-width,initial-scale=1\">\n<meta name=\"theme-color\" content=\"#2A0D45\">\n<link rel=\"preload\" as=\"image\" href=\"/images/sitelogo.png\" imagesizes=\"168px\" imagesrcset=\"/images/sitelogo.png, /images/sitelogo-2x.png 2x\">\n<link rel=\"preload\" as=\"image\" href=\"/shared/images/nst-icons.svg\">\n<link rel=\"stylesheet\" href=\"/shared/css/nst.css?v=2\">\n<script async src=\"/shared/js/nst.js?v=2\"></script>\n<link rel=\"stylesheet\" href=\"/shared/css/nst-foot.css?v=2\" media=\"print\" onload=\"this.media='all'\">\n<link rel=\"stylesheet\" href=\"/site.css\">\n<!--Google Analytics Code-->\n<link rel=\"preload\" href=\"https://www.google-analytics.com/analytics.js\" as=\"script\">\n<script>\n(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]"
    }
  ],
  "http_services": [
    {
      "url": "http://scanme.nmap.org:80/",
      "status_code": 200,
      "title": "Go ahead and ScanMe!",
      "server": "Apache/2.4.7 (Ubuntu)",
      "technologies": [],
      "is_https": false,
      "response_time": 0.3221898078918457
    }
  ],
  "admin_directories": [
    {
      "path": "/server-status",
      "status_code": 403,
      "title": null,
      "is_admin": false,
      "content_type": "text/html; charset=iso-8859-1",
      "response_time": 0.4619109630584717
    }
  ],
  "scan_result": 