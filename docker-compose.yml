services:
  # MCP端口扫描服务器 - 标准MCP stdio协议
  mcp-port-scanner:
    build: .
    container_name: mcp-port-scanner
    volumes:
      - ./scan_results:/app/scan_results
      - ./config:/app/config
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app/src
      - LOG_LEVEL=INFO
    command: tail -f /dev/null  # 保持容器运行，用于MCP stdio服务
    restart: unless-stopped
    networks:
      - scanner-network

  # MCP SSE服务器 - 将stdio MCP转换为HTTP/SSE接口
  mcp-port-scanner-sse:
    build: .
    container_name: mcp-port-scanner-sse
    ports:
      - "3000:3000"
    volumes:
      - ./scan_results:/app/scan_results
      - ./config:/app/config
    environment:
      - PYTHONPATH=/app/src
      - MCP_BIND_HOST=0.0.0.0
      - MCP_PORT=3000
      - LOG_LEVEL=DEBUG
      - LOG_FILE=logs/mcp_port_scanner1.log
      - LOG_DETAILED=true
    command: >
      sh -c "pip install mcp-streamablehttp-proxy && 
             ~/.local/bin/mcp-streamablehttp-proxy --host 0.0.0.0 --port 3000 python -m mcp_port_scanner.interfaces.mcp_local_server"
    restart: unless-stopped
    networks:
      - scanner-network

  # CLI工具容器 - 用于开发调试和独立扫描
  mcp-port-scanner-cli:
    build: .
    container_name: mcp-port-scanner-cli
    volumes:
      - ./scan_results:/app/scan_results
      - ./config:/app/config
    environment:
      - PYTHONPATH=/app/src
    command: tail -f /dev/null  # 保持容器运行，用于执行CLI命令
    networks:
      - scanner-network
    profiles: 
      - tools  # 可选服务，使用 --profile tools 启动

networks:
  scanner-network:
    driver: bridge

volumes:
  scan_results:
  config: 