{"mcpServers": {"port-scanner": {"command": "python", "args": ["-m", "mcp_port_scanner.interfaces.cli_interface", "server", "--mode", "mcp"], "cwd": "/Users/<USER>/Prism/mcp-port-scanner", "env": {"PYTHONPATH": "src"}}, "port-scanner-http": {"command": "python", "args": ["-m", "mcp_port_scanner.interfaces.mcp_local_server", "server", "--mode", "http", "--host", "127.0.0.1", "--port", "8080"], "cwd": "/Users/<USER>/Prism/mcp-port-scanner", "env": {"PYTHONPATH": "src"}}, "port-scanner-stdio": {"command": "python", "args": ["-m", "mcp_port_scanner.interfaces.mcp_local_server"], "cwd": "/Users/<USER>/Prism/mcp-port-scanner", "env": {"PYTHONPATH": "src"}}}}